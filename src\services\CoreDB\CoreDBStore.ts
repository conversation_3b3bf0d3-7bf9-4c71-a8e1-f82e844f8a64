import { defineStore } from 'pinia'
import { ref } from 'vue'
import { CoreDBClient, CoreDBWrapper } from './CoreDBClient'
import { CoreDBWSTransport } from './CoreDBWSTransport'

export const useCoreDBStore = defineStore('CoreDBStore', () => {
    console.log('Initializing CoreDB store')
    const wsTransport = new CoreDBWSTransport('ws://localhost:3000')
    const client = new CoreDBClient(wsTransport)
    const isConnected = ref(false)
    const reconnecting = ref(false)

    const getWrapper = () => {
        console.log('Creating new CoreDBWrapper instance')
        return new CoreDBWrapper(client)
    }

    // Connect immediately
    const connect = async () => {
        try {
            await wsTransport.connect()
            console.log('CoreDB store: Connection established')
            isConnected.value = true
            reconnecting.value = false
        } catch (error) {
            console.error('CoreDB store: Connection failed:', error)
            isConnected.value = false
        }
    }

    // Connection status handlers
    wsTransport.onConnect(() => {
        console.log('CoreDB store: Connected event')
        isConnected.value = true
        reconnecting.value = false
    })

    wsTransport.onDisconnect(() => {
        console.log('CoreDB store: Disconnected event')
        isConnected.value = false
        reconnecting.value = true
    })

    connect()

    return {
        getWrapper,
        isConnected,
        reconnecting,
        connect,
        cleanup: () => {
            console.log('CoreDB store: Cleaning up')
            wsTransport.disconnect()
        }
    }
})
