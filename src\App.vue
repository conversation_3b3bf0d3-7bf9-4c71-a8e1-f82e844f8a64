<template>
	<router-view></router-view>
</template>

<script setup lang="ts">
</script>

<style>
	/* Global styles */
	:root {
		--bg-primary: #1a1a1a;
		--bg-secondary: #2d2d2d;
		--text-primary: #ffffff;
		--text-secondary: #cccccc;
		--border-color: #404040;
	}

	* {
		margin: 0;
		padding: 0;
		box-sizing: border-box;
	}

	html,
	body {
		width: 100vw;
		height: 100vh;
		margin: 0;
		padding: 0;
		overflow: hidden;
	}

	body {
		font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
		line-height: 1.5;
		background-color: var(--bg-primary);
		color: var(--text-primary);
	}
</style>
